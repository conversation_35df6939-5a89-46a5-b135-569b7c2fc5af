import { Injectable, NotFoundException, UnauthorizedException } from '@nestjs/common';
import { InjectRepository, InjectDataSource } from '@nestjs/typeorm';
import { Repository, In, Brackets, DataSource } from 'typeorm';
import { TestRun, TestCaseSelectionType } from './test-run.entity';
import { TestResult, TestResultStatus } from '../test-results/test-result.entity';
import { TestCase } from '../test-cases/test-case.entity';
import { CreateTestRunDto } from './dto/create-test-run.dto';
import { CreateTestResultDto } from '../test-results/dto/create-test-result.dto';
import { ProjectsService } from '../projects/projects.service';
import { TestResultHistory } from '../test-results/test-result-history.entity';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { CreateIssueDto } from '../test-results/dto/issues.dto';
import { Issue } from '../test-results/issue.entity';
import { JiraService } from '../integrations/jira.service';
import { UsersService } from '../users/user.service';
import { User } from '../users/user.entity';
import { ConfigService } from '@nestjs/config';
import { StorageService } from '../test-results/storage.service';

@Injectable()
export class TestRunsService {
  constructor(
    @InjectRepository(TestRun)
    private testRunsRepository: Repository<TestRun>,
    @InjectRepository(TestResult)
    private testResultsRepository: Repository<TestResult>,
    @InjectRepository(TestResultHistory)
    private testResultHistoryRepository: Repository<TestResultHistory>,
    @InjectRepository(TestCase)
    private testCasesRepository: Repository<TestCase>,
    private projectsService: ProjectsService,
    private eventEmitter: EventEmitter2,
    private jiraService: JiraService,
    private usersService: UsersService,
    @InjectDataSource()
    private dataSource: DataSource,
    private configService: ConfigService,
    private storageService: StorageService
  ) {}

  async create(projectId: string, userId: string, createTestRunDto: CreateTestRunDto & { dynamicFilters?: any }): Promise<TestRun> {
    // Get user's companyId
    const user = await this.getUserWithCompanyId(userId);

    // Verify project exists and belongs to user's company
    await this.projectsService.findOne(projectId, user.companyId);

    // Get user information for createdBy field
    let createdBy = 'system';
    try {
      const fullUser = await this.usersService.findById(userId);
      if (fullUser) {
        createdBy = fullUser.name || fullUser.email || 'system';
      }
    } catch (error) {
      console.warn('Failed to get user information for createdBy field:', error);
    }

    // Create the test run first with minimal information
    const testRun = await this.testRunsRepository.save({
      ...createTestRunDto,
      projectId,
      createdBy: createdBy,
      // Store only filter criteria, not the full list of IDs
      selectedTestCaseIds: createTestRunDto.selectionType === TestCaseSelectionType.DYNAMIC
        ? [JSON.stringify(createTestRunDto.dynamicFilters)]
        : (createTestRunDto.selectionType === TestCaseSelectionType.SPECIFIC
           ? createTestRunDto.testCaseIds
           : [])
    });

    // Process test cases in batches to avoid memory issues
    const BATCH_SIZE = 1000;

    // Use a transaction for better performance and data consistency
    await this.dataSource.transaction(async manager => {
      // First delete any existing test results for this test run
      await manager.getRepository(TestResult).delete({ testRunId: testRun.id });

      // Prepare query based on selection type
      let query: any;

      if (createTestRunDto.selectionType === TestCaseSelectionType.ALL) {
        query = manager.getRepository(TestCase)
          .createQueryBuilder('test_case')
          .where('test_case.projectId = :projectId', { projectId });

        // For DAST test runs, filter by test case type even when selection type is ALL
        if (createTestRunDto.type === 'security-dast') {
          query.andWhere('test_case.type = :testCaseType', { testCaseType: 'security - dast' });
        }
      }
      else if (createTestRunDto.selectionType === TestCaseSelectionType.SPECIFIC) {
        // For specific test cases, process them in batches
        const testCaseIds = createTestRunDto.testCaseIds || [];
        for (let i = 0; i < testCaseIds.length; i += BATCH_SIZE) {
          const batchIds = testCaseIds.slice(i, i + BATCH_SIZE);
          await this.processTestCaseBatch(manager, testRun.id, batchIds, projectId, createdBy);
        }
        return; // Exit early as we've already processed everything in batches
      }
      else if (createTestRunDto.selectionType === TestCaseSelectionType.DYNAMIC) {
        query = this.buildDynamicFilterQuery(
          manager.getRepository(TestCase).createQueryBuilder('test_case'),
          projectId,
          createTestRunDto.dynamicFilters,
          createTestRunDto.type
        );
      }

      // For ALL or DYNAMIC selection types, process in batches
      const totalCount = await query.getCount();
      const totalPages = Math.ceil(totalCount / BATCH_SIZE);

      for (let page = 0; page < totalPages; page++) {
        const testCases = await query
          .skip(page * BATCH_SIZE)
          .take(BATCH_SIZE)
          .getMany();

        await this.createTestResultsForBatch(manager, testRun.id, testCases, createdBy);
      }
    });

    this.eventEmitter.emit('testRun.created', { projectId: projectId, testRunId: testRun.id });

    return testRun;
  }

  // Helper method to build dynamic filter query
  private buildDynamicFilterQuery(queryBuilder: any, projectId: string, dynamicFilters: any, testRunType?: string) {
    queryBuilder.where('test_case.projectId = :projectId', { projectId });

    // For DAST test runs, always filter by test case type
    if (testRunType === 'security-dast') {
      queryBuilder.andWhere('test_case.type = :testCaseType', { testCaseType: 'security - dast' });
    }

    if (dynamicFilters?.priorities?.length) {
      queryBuilder.andWhere('test_case.priority IN (:...priorities)', {
        priorities: dynamicFilters.priorities
      });
    }

    if (dynamicFilters?.platforms?.length) {
      queryBuilder.andWhere('test_case.platform IN (:...platforms)', {
        platforms: dynamicFilters.platforms
      });
    }

    if (dynamicFilters?.testCaseTypes?.length) {
      queryBuilder.andWhere('test_case.testCaseType IN (:...testCaseTypes)', {
        testCaseTypes: dynamicFilters.testCaseTypes
      });
    }

    if (dynamicFilters?.folders?.length) {
      const folders = dynamicFilters.folders;
      if (folders.includes('none')) {
        queryBuilder.andWhere(new Brackets(qb => {
          qb.where('test_case.folderId IS NULL')
            .orWhere('test_case.folderId IN (:...folders)', {
              folders: folders.filter((f: any) => f !== 'none')
            });
        }));
      } else {
        queryBuilder.andWhere('test_case.folderId IN (:...folders)', { folders });
      }
    }

    if (dynamicFilters?.tags?.length) {
      const tags = dynamicFilters.tags;

      if (tags.includes('none')) {
        queryBuilder.leftJoin('test_case.tags', 'tags')
          .andWhere(new Brackets(qb => {
            qb.where('tags.id IS NULL')
              .orWhere('tags.id IN (:...tagIds)', {
                tagIds: tags.filter((t: any) => t !== 'none')
              });
          }));
      } else {
        queryBuilder.innerJoin('testcase_tags', 'tt', 'tt.testcaseId = test_case.id')
          .andWhere('tt.tagId IN (:...tagIds)', {
            tagIds: tags
          });
      }
    }

    return queryBuilder;
  }

  // Helper method to process a batch of test cases
  private async processTestCaseBatch(manager: any, testRunId: string, testCaseIds: string[], projectId: string, createdBy = 'system') {
    if (testCaseIds.length === 0) return;

    const testCases = await manager.getRepository(TestCase).find({
      where: {
        id: In(testCaseIds),
        projectId
      }
    });

    await this.createTestResultsForBatch(manager, testRunId, testCases, createdBy);
  }

  // Helper method to create test results for a batch
  private async createTestResultsForBatch(manager: any, testRunId: string, testCases: TestCase[], createdBy = 'system') {
    if (testCases.length === 0) return;

    // Create test results
    const testResults = testCases.map(testCase => ({
      testRunId: testRunId,
      testCaseId: testCase.id,
      status: TestResultStatus.UNTESTED,
      sequence: 1,
      isLatest: true,
      createdBy: createdBy
    }));

    const savedTestResults = await manager.getRepository(TestResult).save(testResults);

    // Create initial history records
    const historyRecords = savedTestResults.map((result: any) => ({
      testResultId: result.id,
      previousStatus: TestResultStatus.UNTESTED,
      previousActualResult: null,
      previousNotes: null
    }));

    await manager.getRepository(TestResultHistory).save(historyRecords);
  }

  // Handle test case created event
  @OnEvent('testCase.created')
  async handleTestCaseCreatedEvent(payload: { projectId: string }) {
    const { projectId } = payload;

    // Find all test runs for the project
    const testRuns = await this.testRunsRepository.find({
      where: {
        projectId,
      },
    });

    // Update each test run
    for (const testRun of testRuns) {
      await this.updateTestRunResults(testRun.id, projectId, testRun.selectionType, testRun.selectedTestCaseIds, testRun.type); // Pass selectionType, selectedTestCaseIds, and type
    }
  }

  @OnEvent('testCase.updated') // Listen for both created and updated events
  async handleTestCaseChangedEvent(payload: { projectId: string }) {
    const { projectId } = payload;

    // Find all test runs for the project
    const testRuns = await this.testRunsRepository.find({
      where: {
        projectId,
      },
    });

    // Update each test run
    for (const testRun of testRuns) {
      await this.updateTestRunResults(testRun.id, projectId, testRun.selectionType, testRun.selectedTestCaseIds, testRun.type);
    }
  }

  async updateTestRunResults(testRunId: string, projectId: string, selectionType: TestCaseSelectionType, selectedTestCaseIds: string[], testRunType?: string) {
    let testCases: TestCase[] = [];

    if (selectionType === TestCaseSelectionType.ALL) {
      const whereCondition: any = { projectId };

      // For DAST test runs, filter by test case type
      if (testRunType === 'security-dast') {
        whereCondition.type = 'security - dast';
      }

      testCases = await this.testCasesRepository.find({ where: whereCondition });
    } else if (selectionType === TestCaseSelectionType.SPECIFIC) {
      testCases = await this.testCasesRepository.find({
        where: {
          id: In(selectedTestCaseIds || []),
          projectId,
        },
      });
    } else if (selectionType === TestCaseSelectionType.DYNAMIC) {
      if (!selectedTestCaseIds || selectedTestCaseIds.length === 0) {
        testCases = [];
      } else {
        try {
          const filterCriteria = JSON.parse(selectedTestCaseIds[0]);

          const queryBuilder = this.buildDynamicFilterQuery(
            this.testCasesRepository.createQueryBuilder('test_case'),
            projectId,
            filterCriteria,
            testRunType
          );

          testCases = await queryBuilder.getMany();
        } catch (e) {
          console.error('Failed to parse dynamic filters:', e);
          testCases = [];
        }
      }
    }

    // Get existing test results for this test run
    const existingResults = await this.testResultsRepository.find({
      where: { testRunId, isLatest: true },
    });

    // Create a map of existing test results for easier lookup
    const existingResultsMap = new Map(existingResults.map(result => [result.testCaseId, result]));

    // Update or create test results
    for (const testCase of testCases) {
      const existingResult = existingResultsMap.get(testCase.id);

      if (existingResult) {
        // Update existing test result
        await this.testResultsRepository.update(existingResult.id, {}); // No actual changes needed, just touch the record
      } else {
        // Create new test result
        const newTestResult = this.testResultsRepository.create({
          testRunId,
          testCaseId: testCase.id,
          status: TestResultStatus.UNTESTED,
          sequence: 1,
          isLatest: true,
          createdBy: 'system'
        });

        const savedTestResult = await this.testResultsRepository.save(newTestResult);

        // Create initial history record
        const historyRecord = this.testResultHistoryRepository.create({
          testResultId: savedTestResult.id,
          previousStatus: TestResultStatus.UNTESTED,
          previousActualResult: null,
          previousNotes: null,
        });

        await this.testResultHistoryRepository.save(historyRecord);
      }
    }

    // Remove test results for test cases that are no longer in the test run
    const testCaseIds = testCases.map(tc => tc.id);
    for (const existingResult of existingResults) {
      if (!testCaseIds.includes(existingResult.testCaseId)) {
        await this.testResultsRepository.delete(existingResult.id);
      }
    }
  }

  async findAll(projectId: string, userId: string, options: { page: number; limit: number; search?: string }) {
    // Get user's companyId
    const user = await this.getUserWithCompanyId(userId);
    
    // Verify project exists and belongs to user's company
    await this.projectsService.findOne(projectId, user.companyId);

    const { page, limit, search } = options;
    const skip = (page - 1) * limit;

    const queryBuilder = this.testRunsRepository.createQueryBuilder('test_run')
      .where('test_run.projectId = :projectId', { projectId });

    if (search) {
      queryBuilder.andWhere('(test_run.name ILIKE :search OR test_run.description ILIKE :search)', {
        search: `%${search}%`
      });
    }

    const [testRuns, total] = await queryBuilder
      .skip(skip)
      .take(limit)
      .orderBy('test_run.createdAt', 'DESC')
      .getManyAndCount();

    return {
      testRuns,
      total,
      page,
      totalPages: Math.ceil(total / limit)
    };
  }

  async findOne(id: string, projectId: string, userId: string): Promise<TestRun> {
    // Get user's companyId
    const user = await this.getUserWithCompanyId(userId);
    
    // First fetch the test run
    const testRun = await this.testRunsRepository.findOne({
      where: { id }
    });

    if (!testRun) {
      throw new NotFoundException(`Test run with ID ${id} not found`);
    }

    // Verify project exists and belongs to user's company
    await this.projectsService.findOne(testRun.projectId, user.companyId);

    // Verify that the test run belongs to the specified project
    if (testRun.projectId !== projectId) {
      throw new NotFoundException(`Test run with ID ${id} does not belong to project ${projectId}`);
    }

    return testRun;
  }

  async update(id: string, projectId: string, userId: string, updateTestRunDto: Partial<CreateTestRunDto>): Promise<TestRun> {
    const testRun = await this.findOne(id, projectId, userId);

    let testCases: TestCase[] = [];
    let shouldUpdateTestResults = false;

    // Check if it's a dynamic test run
    if (updateTestRunDto.selectionType === 'dynamic' && updateTestRunDto.dynamicFilters) {
        const dynamicFilters = updateTestRunDto.dynamicFilters;

        // Determine if filters have changed for dynamic runs
        let currentFilters = {};
        if (testRun.selectionType === 'dynamic' && testRun.selectedTestCaseIds?.length > 0) {
            try {
                currentFilters = JSON.parse(testRun.selectedTestCaseIds[0]);
            } catch (e) {
                console.error('Failed to parse existing dynamic filters:', e);
            }
        }

        // Compare filters to see if they've changed
        if (JSON.stringify(currentFilters) !== JSON.stringify(dynamicFilters)) {
            shouldUpdateTestResults = true;
        }

        // Fetch test cases based on dynamic filters
        const queryBuilder = this.buildDynamicFilterQuery(
            this.testCasesRepository.createQueryBuilder('test_case'),
            projectId,
            dynamicFilters,
            updateTestRunDto.type || testRun.type
        );

        testCases = await queryBuilder.getMany();
        const testCaseIds = testCases.map(tc => tc.id);
        updateTestRunDto.testCaseIds = testCaseIds;
    } else if (updateTestRunDto.testCaseIds) {
        // For non-dynamic runs, check if test cases have changed
        if (JSON.stringify(testRun.selectedTestCaseIds) !== JSON.stringify(updateTestRunDto.testCaseIds)) {
            shouldUpdateTestResults = true;
        }

        // Fetch test cases to verify they exist
        testCases = await this.testCasesRepository.find({
            where: {
                id: In(updateTestRunDto.testCaseIds),
                projectId,
            },
        });

        if (testCases.length !== updateTestRunDto.testCaseIds.length) {
            throw new NotFoundException('One or more test cases not found');
        }
    } else if (updateTestRunDto.type && updateTestRunDto.type !== testRun.type) {
        // If only the type has changed, we need to refetch test cases based on the current selection
        shouldUpdateTestResults = true;

        if (testRun.selectionType === TestCaseSelectionType.ALL) {
            const whereCondition: any = { projectId };

            // Apply type filtering for DAST test runs
            if (updateTestRunDto.type === 'security-dast') {
                whereCondition.type = 'security - dast';
            }

            testCases = await this.testCasesRepository.find({ where: whereCondition });
        } else if (testRun.selectionType === TestCaseSelectionType.SPECIFIC) {
            // For specific selection, keep the same test cases but filter by type if needed
            const whereCondition: any = {
                id: In(testRun.selectedTestCaseIds || []),
                projectId,
            };

            // Apply type filtering for DAST test runs
            if (updateTestRunDto.type === 'security-dast') {
                whereCondition.type = 'security - dast';
            }

            testCases = await this.testCasesRepository.find({ where: whereCondition });
        } else if (testRun.selectionType === TestCaseSelectionType.DYNAMIC) {
            // For dynamic selection, reapply the filters with the new type
            if (testRun.selectedTestCaseIds?.length > 0) {
                try {
                    const filterCriteria = JSON.parse(testRun.selectedTestCaseIds[0]);
                    const queryBuilder = this.buildDynamicFilterQuery(
                        this.testCasesRepository.createQueryBuilder('test_case'),
                        projectId,
                        filterCriteria,
                        updateTestRunDto.type
                    );
                    testCases = await queryBuilder.getMany();
                } catch (e) {
                    console.error('Failed to parse existing dynamic filters:', e);
                    testCases = [];
                }
            }
        }
    }

    // Update test results if needed
    if (shouldUpdateTestResults ||
        (updateTestRunDto.selectionType === 'dynamic' && testRun.selectionType !== 'dynamic') ||
        (updateTestRunDto.selectionType !== 'dynamic' && testRun.selectionType === 'dynamic') ||
        (updateTestRunDto.type && updateTestRunDto.type !== testRun.type)) {

        // Get existing test results for this test run
        const existingResults = await this.testResultsRepository.find({
            where: { testRunId: id, isLatest: true },
        });

        // Create a map of existing test results for easier lookup
        const existingResultsMap = new Map(existingResults.map(result => [result.testCaseId, result]));

        // Update or create test results
        for (const testCase of testCases) {
            const existingResult = existingResultsMap.get(testCase.id);

            if (existingResult) {
                // Update existing test result (no status change)
                await this.testResultsRepository.update(existingResult.id, {});
            } else {
                // Create new test result
                const newTestResult = this.testResultsRepository.create({
                    testRunId: id,
                    testCaseId: testCase.id,
                    status: TestResultStatus.UNTESTED,
                    sequence: 1,
                    isLatest: true,
                    createdBy: 'system'
                });

                const savedTestResult = await this.testResultsRepository.save(newTestResult);

                // Create initial history record
                const historyRecord = this.testResultHistoryRepository.create({
                    testResultId: savedTestResult.id,
                    previousStatus: TestResultStatus.UNTESTED,
                    previousActualResult: null,
                    previousNotes: null,
                });

                await this.testResultHistoryRepository.save(historyRecord);
            }
        }

        // Remove test results for test cases that are no longer in the test run
        const testCaseIds = testCases.map(tc => tc.id);
        for (const existingResult of existingResults) {
            if (!testCaseIds.includes(existingResult.testCaseId)) {
                await this.testResultsRepository.delete(existingResult.id);
            }
        }

        // Emit the test run updated event
        this.eventEmitter.emit('testRun.updated', { projectId: projectId, testRunId: id });
    }

    // Update test run data
    Object.assign(testRun, {
        ...updateTestRunDto,
    });

    // Set selectedTestCaseIds correctly based on selection type
    if (updateTestRunDto.selectionType === 'dynamic' && updateTestRunDto.dynamicFilters) {
        testRun.selectedTestCaseIds = [JSON.stringify(updateTestRunDto.dynamicFilters)];
    } else if (updateTestRunDto.testCaseIds) {
        testRun.selectedTestCaseIds = updateTestRunDto.testCaseIds;
    }

    return this.testRunsRepository.save(testRun);
}

  async remove(id: string, projectId: string, userId: string): Promise<void> {
    const testRun = await this.findOne(id, projectId, userId);

    try {
      // Delete associated files from Google Cloud Storage
      console.log(`Cleaning up Google Cloud Storage data for test run: ${id}`);
      await this.storageService.deleteTestRunData(projectId, id);
      console.log(`Successfully cleaned up Google Cloud Storage data for test run: ${id}`);
    } catch (error) {
      console.error(`Failed to cleanup Google Cloud Storage data for test run ${id}:`, error);
      // Continue with database deletion even if storage cleanup fails
    }

    // Delete the test run from database
    await this.testRunsRepository.remove(testRun);
    console.log(`Successfully deleted test run from database: ${id}`);
  }

  async removeBatch(ids: string[], projectId: string, _userId: string): Promise<void> {
    // Find all test runs to ensure user has access to all of them
    const testRuns = await this.testRunsRepository.findBy({
      id: In(ids),
      projectId,
      // Add any additional conditions for user permissions if needed
    });

    // Verify that all requested IDs were found
    if (testRuns.length !== ids.length) {
      throw new NotFoundException('One or more test runs not found or access denied');
    }

    // Clean up Google Cloud Storage data for each test run
    for (const testRun of testRuns) {
      try {
        console.log(`Cleaning up Google Cloud Storage data for test run: ${testRun.id}`);
        await this.storageService.deleteTestRunData(projectId, testRun.id);
        console.log(`Successfully cleaned up Google Cloud Storage data for test run: ${testRun.id}`);
      } catch (error) {
        console.error(`Failed to cleanup Google Cloud Storage data for test run ${testRun.id}:`, error);
        // Continue with other test runs even if one cleanup fails
      }
    }

    // Delete all test runs in a single transaction
    await this.dataSource.transaction(async manager => {
      await manager.remove(testRuns);
    });
    console.log(`Successfully deleted ${testRuns.length} test runs from database`);
  }

  async createTestResult(testRunId: string, projectId: string, userId: string, createTestResultDto: CreateTestResultDto): Promise<TestResult> {
    return this.testResultsRepository.manager.transaction(async (manager) => {
      const testRun = await this.findOne(testRunId, projectId, userId);

      // Get user information for createdBy field
      let createdBy = 'unknown';

      // First, try to use createdBy from DTO if provided (from websocket)
      if (createTestResultDto.createdBy) {
        createdBy = createTestResultDto.createdBy;
        console.log('Using createdBy from DTO:', createdBy);
      } else {
        // Fallback to user lookup from JWT token
        try {
          const user = await this.usersService.findById(userId);
          if (user) {
            createdBy = user.name || user.email || 'unknown';
            console.log('Using createdBy from user lookup:', createdBy);
          }
        } catch (error) {
          console.warn('Failed to get user information for createdBy field:', error);
        }
      }

      // Check if there's an existing test result for this test case
      const existingResult = await manager.findOne(TestResult, {
        where: {
          testRunId: testRun.id,
          testCaseId: createTestResultDto.testCaseId,
          isLatest: true
        }
      });

      // If there's an existing result, mark it as not latest
      if (existingResult) {
        // Find the highest sequence number
        const maxSequenceResult = await manager.createQueryBuilder()
          .select('MAX(sequence)', 'maxSequence')
          .from(TestResult, 'tr')
          .where('tr.testCaseId = :testCaseId', { testCaseId: createTestResultDto.testCaseId })
          .andWhere('tr.testRunId = :testRunId', { testRunId: testRun.id })
          .getRawOne();

        const nextSequence = maxSequenceResult.maxSequence + 1;

        // Mark existing result as not latest
        await manager.update(TestResult, { id: existingResult.id }, { isLatest: false });

        // Create new test result with next sequence and reference to previous
        // Extract duration from logs if available
        let duration: number | null = null;
        if (createTestResultDto.logs && createTestResultDto.logs.length > 0) {
          const executionTimeLog = createTestResultDto.logs.find(log =>
            log.includes('Execution time:') || log.includes('⏱️ Execution time:')
          );
          if (executionTimeLog) {
            const match = executionTimeLog.match(/(\d+)ms/);
            if (match) {
              duration = parseInt(match[1]);
            }
          }
        }

        const testResult = manager.create(TestResult, {
          ...createTestResultDto,
          testRunId: testRun.id,
          sequence: nextSequence,
          previousResultId: existingResult.id,
          isLatest: true,
          duration: duration, // Add extracted duration
          createdBy: createdBy
        });

        const savedTestResult = await manager.save(TestResult, testResult);

        // Handle logs storage if logs are provided
        if (createTestResultDto.logs && createTestResultDto.logs.length > 0) {
          try {
            const logsUrl = await this.storageService.uploadLogs(
              {
                projectId: projectId,
                testCaseId: createTestResultDto.testCaseId,
                testRunId: testRunId,
                testResultId: savedTestResult.id
              },
              createTestResultDto.logs
            );
            savedTestResult.logsUrl = logsUrl;
            await manager.save(TestResult, savedTestResult);
          } catch (error) {
            console.error(`Failed to upload logs for test result ${savedTestResult.id}:`, error);
            // Continue without failing the entire operation
          }
        }

        return savedTestResult;
      } else {
        // Create new test result as the first one
        // Extract duration from logs if available
        let duration: number | null = null;
        if (createTestResultDto.logs && createTestResultDto.logs.length > 0) {
          const executionTimeLog = createTestResultDto.logs.find(log =>
            log.includes('Execution time:') || log.includes('⏱️ Execution time:')
          );
          if (executionTimeLog) {
            const match = executionTimeLog.match(/(\d+)ms/);
            if (match) {
              duration = parseInt(match[1]);
            }
          }
        }

        const testResult = manager.create(TestResult, {
          ...createTestResultDto,
          testRunId: testRun.id,
          sequence: 1,
          isLatest: true,
          duration: duration, // Add extracted duration
          createdBy: createdBy
        });

        const savedTestResult = await manager.save(TestResult, testResult);

        // Handle logs storage if logs are provided
        if (createTestResultDto.logs && createTestResultDto.logs.length > 0) {
          try {
            const logsUrl = await this.storageService.uploadLogs(
              {
                projectId: projectId,
                testCaseId: createTestResultDto.testCaseId,
                testRunId: testRunId,
                testResultId: savedTestResult.id
              },
              createTestResultDto.logs
            );
            savedTestResult.logsUrl = logsUrl;
            await manager.save(TestResult, savedTestResult);
          } catch (error) {
            console.error(`Failed to upload logs for test result ${savedTestResult.id}:`, error);
            // Continue without failing the entire operation
          }
        }

        return savedTestResult;
      }
    });
  }

  async createTestResultWithApiKey(
    testRunId: string,
    projectId: string,
    apiKey: string,
    createTestResultDto: CreateTestResultDto
  ): Promise<TestResult> {
    // Validate API key against JWT_SECRET
    const isValidApiKey = apiKey === this.configService.get('JWT_SECRET');
    if (!isValidApiKey) {
      throw new UnauthorizedException('Invalid API key');
    }
    
    // Find the test run without user validation
    const testRun = await this.testRunsRepository.findOne({
      where: { id: testRunId, projectId }
    });
    
    if (!testRun) {
      throw new NotFoundException(`Test run with ID ${testRunId} not found in project ${projectId}`);
    }
    
    // Create the test result
    const testResult = new TestResult();
    Object.assign(testResult, {
      ...createTestResultDto,
      testRunId,
      projectId,
      // Set userId to null or a system user ID if needed
      userId: 'system',
      status: createTestResultDto.status || TestResultStatus.UNTESTED,
      createdBy: 'system'
    });
    
    return this.testResultsRepository.save(testResult);
  }

  async findAllTestResults(
    testRunId: string,
    projectId: string,
    userId: string,
    options: {
      page: number;
      limit: number;
      status?: string;
      search?: string;
      sortField?: string;
      sortDirection?: 'ASC' | 'DESC';
      filters?: {
        status?: string[];
        priority?: string[];
        tagIds?: string[];
        type?: string[];
      }
    }
  ): Promise<{ results: TestResult[]; total: number; totalPages: number }> {
    // Verify access to test run
    await this.findOne(testRunId, projectId, userId);
    const { page, limit, status, search, sortField, sortDirection, filters } = options;
    const skip = (page - 1) * limit;

    // Create query builder for test results
    const queryBuilder = this.testResultsRepository
      .createQueryBuilder('result')
      .leftJoinAndSelect('result.testCase', 'testCase')
      .leftJoinAndSelect('testCase.tags', 'tags')
      .where('result.testRunId = :testRunId', { testRunId })
      .andWhere('result.isLatest = :isLatest', { isLatest: true });

    // Add legacy status filter if provided (for backward compatibility)
    if (status) {
      queryBuilder.andWhere('result.status = :status', { status });
    }

    // Add advanced filters if provided
    if (filters) {
      // Filter by status
      if (filters.status && filters.status.length > 0) {
        // Handle both array and string formats
        const statuses = Array.isArray(filters.status) ? filters.status : [filters.status];
        queryBuilder.andWhere('result.status IN (:...statuses)', {
          statuses
        });
      }

      // Filter by priority
      if (filters.priority && filters.priority.length > 0) {
        // Handle both array and string formats
        const priorities = Array.isArray(filters.priority) ? filters.priority : [filters.priority];
        queryBuilder.andWhere('testCase.priority IN (:...priorities)', {
          priorities
        });
      }

      // Filter by tag
      if (filters.tagIds && filters.tagIds.length > 0) {
        // Handle both array and string formats
        const tagIds = Array.isArray(filters.tagIds) ? filters.tagIds : [filters.tagIds];
        queryBuilder.andWhere('tags.id IN (:...tagIds)', {
          tagIds
        });
      }

      // Filter by test case type
      if (filters.type && filters.type.length > 0) {
        // Handle both array and string formats
        const types = Array.isArray(filters.type) ? filters.type : [filters.type];
        queryBuilder.andWhere('testCase.testCaseType IN (:...types)', {
          types
        });
      }
    }

    // Add search filter if provided
    if (search) {
      const searchTerm = search.toLowerCase();
      const searchTermLike = `%${searchTerm}%`;
      const searchTermWithoutPrefix = searchTerm.startsWith('tc-')
        ? searchTerm.substring(3)
        : searchTerm;

      queryBuilder.andWhere(new Brackets(qb => {
        qb.where('CAST(testCase.tcId AS TEXT) LIKE :tcId', { tcId: `%${searchTermWithoutPrefix}%` })
          .orWhere('LOWER(testCase.title) LIKE :searchLike', { searchLike: searchTermLike })
          .orWhere('LOWER(testCase.type) LIKE :searchLike', { searchLike: searchTermLike })
          .orWhere('CAST(testCase.priority AS TEXT) ILIKE :searchLike', { searchLike: searchTermLike })
          .orWhere('CAST(testCase.platform AS TEXT) ILIKE :searchLike', { searchLike: searchTermLike })
          .orWhere('CAST(testCase.testCaseType AS TEXT) ILIKE :searchLike', { searchLike: searchTermLike })
          .orWhere('LOWER(tags.name) LIKE :searchLike', { searchLike: searchTermLike });
      }));
    }

    // Get total count
    const total = await queryBuilder.getCount();

    // Add sorting
    const fieldToSort = sortField || 'tcId';
    // Ensure direction is uppercase and valid
    let directionToSort = (sortDirection || 'ASC').toUpperCase();
    if (directionToSort !== 'ASC' && directionToSort !== 'DESC') {
      directionToSort = 'ASC';
    }

    console.log(`Sorting by ${fieldToSort} in direction ${directionToSort}`);

    // Cast to the correct type for TypeORM
    const sortDir = directionToSort === 'DESC' ? 'DESC' : 'ASC';

    // Handle different sort fields
    if (fieldToSort === 'tcId') {
      queryBuilder.orderBy('testCase.tcId', sortDir);
    } else if (fieldToSort === 'title') {
      queryBuilder.orderBy('testCase.title', sortDir);
    } else if (fieldToSort === 'priority') {
      queryBuilder.orderBy('testCase.priority', sortDir);
    } else if (fieldToSort === 'status') {
      queryBuilder.orderBy('result.status', sortDir);
    } else if (fieldToSort === 'testCaseType' || fieldToSort === 'type') {
      queryBuilder.orderBy('testCase.testCaseType', sortDir);
    } else if (fieldToSort === 'platform') {
      queryBuilder.orderBy('testCase.platform', sortDir);
    } else {
      // Default sort
      queryBuilder.orderBy('testCase.tcId', 'ASC');
    }

    // Add pagination
    const results = await queryBuilder
      .skip(skip)
      .take(limit)
      .getMany();

    return {
      results,
      total,
      totalPages: Math.ceil(total / limit)
    };
  }

  async getTestRunSummary(testRunId: string, projectId: string, userId: string) {
    // Verify access to test run
    await this.findOne(testRunId, projectId, userId);

    const summary = await this.testResultsRepository
      .createQueryBuilder('result')
      .select('result.status', 'status')
      .addSelect('COUNT(*)', 'count')
      .where('result.testRunId = :testRunId', { testRunId })
      .andWhere('result.isLatest = :isLatest', { isLatest: true })
      .groupBy('result.status')
      .getRawMany();

    const statusCounts = {
      passed: 0,
      failed: 0,
      blocked: 0,
      skipped: 0,
      untested: 0,
    };

    summary.forEach(item => {
      statusCounts[item.status] = parseInt(item.count);
    });

    const total = Object.values(statusCounts).reduce((a, b) => a + b, 0);

    return {
      total,
      ...statusCounts,
    };
  }

  async getDastSummary(testRunId: string, projectId: string, userId: string) {
    // Verify access to test run
    await this.findOne(testRunId, projectId, userId);

    // Get vulnerability counts from test results using the existing columns
    const vulnerabilityQuery = await this.testResultsRepository
      .createQueryBuilder('result')
      .select('SUM(result.highCount)', 'high')
      .addSelect('SUM(result.mediumCount)', 'medium')
      .addSelect('SUM(result.lowCount)', 'low')
      .addSelect('SUM(result.informationalCount)', 'info')
      .addSelect('SUM(result.falsePositiveCount)', 'falsePositive')
      .where('result.testRunId = :testRunId', { testRunId })
      .andWhere('result.isLatest = :isLatest', { isLatest: true })
      .getRawOne();

    // Initialize vulnerability counts
    const vulnerabilities = {
      critical: 0, // Not tracked in current schema, could be added later
      high: parseInt(vulnerabilityQuery?.high) || 0,
      medium: parseInt(vulnerabilityQuery?.medium) || 0,
      low: parseInt(vulnerabilityQuery?.low) || 0,
      falsePositive: parseInt(vulnerabilityQuery?.falsePositive) || 0
    };

    // Get execution status counts for DAST-specific statuses
    const executionStatusQuery = await this.testResultsRepository
      .createQueryBuilder('result')
      .select('result.status', 'status')
      .addSelect('COUNT(*)', 'count')
      .where('result.testRunId = :testRunId', { testRunId })
      .andWhere('result.isLatest = :isLatest', { isLatest: true })
      .groupBy('result.status')
      .getRawMany();

    const executionStatus = {
      new: 0,
      open: 0,
      inProgress: 0,
      fixedRemediated: 0,
      verifiedRetested: 0,
      closed: 0,
      falsePositive: 0,
      acceptedRisk: 0,
      duplicate: 0
    };

    // Map status counts to execution status
    executionStatusQuery.forEach(item => {
      const status = item.status;
      const count = parseInt(item.count);

      switch (status) {
        case 'New':
          executionStatus.new = count;
          break;
        case 'Open':
          executionStatus.open = count;
          break;
        case 'In Progress':
          executionStatus.inProgress = count;
          break;
        case 'Fixed/Remediated':
          executionStatus.fixedRemediated = count;
          break;
        case 'Verified/Re-tested':
          executionStatus.verifiedRetested = count;
          break;
        case 'Closed':
          executionStatus.closed = count;
          break;
        case 'False Positive':
          executionStatus.falsePositive = count;
          break;
        case 'Accepted Risk / Waived':
          executionStatus.acceptedRisk = count;
          break;
        case 'Duplicate':
          executionStatus.duplicate = count;
          break;
      }
    });

    // Get test coverage metrics (percentage of DAST test cases executed)
    const totalTestCases = await this.testResultsRepository.count({
      where: { testRunId, isLatest: true }
    });

    const executedTestCases = await this.testResultsRepository.count({
      where: {
        testRunId,
        isLatest: true,
        status: In(['passed', 'failed', 'New', 'Open', 'In Progress', 'Fixed/Remediated', 'Verified/Re-tested', 'Closed', 'False Positive', 'Accepted Risk / Waived', 'Duplicate'])
      }
    });

    const testCoverage = {
      totalTestCases: totalTestCases,
      executedTestCases: executedTestCases,
      coveragePercentage: totalTestCases > 0 ? Math.round((executedTestCases / totalTestCases) * 100 * 10) / 10 : 0
    };

    // Get security test type metrics based on test case types/tags
    const securityMetricsQuery = await this.testResultsRepository
      .createQueryBuilder('result')
      .leftJoin('result.testCase', 'testCase')
      .select('testCase.type', 'type')
      .addSelect('COUNT(*)', 'count')
      .where('result.testRunId = :testRunId', { testRunId })
      .andWhere('result.isLatest = :isLatest', { isLatest: true })
      .andWhere('testCase.type IS NOT NULL')
      .groupBy('testCase.type')
      .getRawMany();

    const securityMetrics = {
      authenticationTests: 0,
      authorizationTests: 0,
      inputValidationTests: 0,
      sessionManagementTests: 0
    };

    // Map test types to security metrics
    securityMetricsQuery.forEach(item => {
      const type = item.type?.toLowerCase();
      if (type?.includes('auth') && !type.includes('authorization')) {
        securityMetrics.authenticationTests += parseInt(item.count);
      } else if (type?.includes('authorization') || type?.includes('access')) {
        securityMetrics.authorizationTests += parseInt(item.count);
      } else if (type?.includes('input') || type?.includes('validation') || type?.includes('injection')) {
        securityMetrics.inputValidationTests += parseInt(item.count);
      } else if (type?.includes('session') || type?.includes('cookie')) {
        securityMetrics.sessionManagementTests += parseInt(item.count);
      }
    });

    return {
      vulnerabilities,
      executionStatus,
      testCoverage,
      securityMetrics
    };
  }

  async findOneTestResult(id: string, projectId: string, userId: string): Promise<TestResult> {
    const testResult = await this.testResultsRepository.findOne({
      where: { id },
      relations: ['testRun'],
    });

    if (!testResult || testResult.testRun.projectId !== projectId) {
      throw new NotFoundException('Test result not found');
    }

    // Verify access
    await this.findOne(testResult.testRunId, projectId, userId);

    return testResult;
  }

  async updateTestResult(
    id: string,
    projectId: string,
    userId: string,
    updateTestResultDto: Partial<CreateTestResultDto>
  ): Promise<TestResult> {
    return this.testResultsRepository.manager.transaction(async (manager) => {
      // 1. First lock the test result
      await manager.query(
        `SELECT 1 FROM test_results WHERE id = $1 FOR UPDATE`,
        [id]
      );

      // 2. Then load with relations
      const currentTestResult = await manager.findOne(TestResult, {
        where: { id, isLatest: true },
        relations: ['testRun', 'testCase']
      });

      if (!currentTestResult || currentTestResult.testRun.projectId !== projectId) {
        throw new NotFoundException('Test result not found');
      }

      // 3. Find the highest sequence number for this test case in this test run
      const maxSequenceResult = await manager.createQueryBuilder()
        .select('MAX(sequence)', 'maxSequence')
        .from(TestResult, 'tr')
        .where('tr.testCaseId = :testCaseId', { testCaseId: currentTestResult.testCaseId })
        .andWhere('tr.testRunId = :testRunId', { testRunId: currentTestResult.testRunId })
        .getRawOne();

      const nextSequence = maxSequenceResult.maxSequence + 1;

      // 4. Mark the current result as not latest
      await manager.update(TestResult, { id }, { isLatest: false });

      // 5. Extract duration from logs if available (same logic as createTestResult)
      let duration: number | null = updateTestResultDto.duration || currentTestResult.duration;
      if (updateTestResultDto.logs && updateTestResultDto.logs.length > 0) {
        const executionTimeLog = updateTestResultDto.logs.find(log =>
          log.includes('Execution time:') || log.includes('⏱️ Execution time:')
        );
        if (executionTimeLog) {
          const match = executionTimeLog.match(/(\d+)ms/);
          if (match) {
            duration = parseInt(match[1]);
          }
        }
      }

      // 6. Get user information for createdBy field
      let createdBy = 'unknown';

      // First, try to use createdBy from DTO if provided (from websocket)
      if (updateTestResultDto.createdBy) {
        createdBy = updateTestResultDto.createdBy;
        console.log('Using createdBy from DTO:', createdBy);
      } else {
        // Fallback to user lookup from JWT token
        try {
          const user = await this.usersService.findById(userId);
          if (user) {
            createdBy = user.name || user.email || 'unknown';
            console.log('Using createdBy from user lookup:', createdBy);
          }
        } catch (error) {
          console.warn('Failed to get user information for createdBy field:', error);
        }
      }

      // 7. Create a new test result record
      const newTestResult = manager.create(TestResult, {
        testRunId: currentTestResult.testRunId,
        testCaseId: currentTestResult.testCaseId,
        status: updateTestResultDto.status || currentTestResult.status,
        actualResult: updateTestResultDto.actualResult !== undefined ? updateTestResultDto.actualResult : currentTestResult.actualResult,
        notes: updateTestResultDto.notes !== undefined ? updateTestResultDto.notes : currentTestResult.notes,
        duration: duration, // Use extracted duration, not executionTime
        // DAST-specific fields
        vulnerabilityDescription: updateTestResultDto.vulnerabilityDescription !== undefined ? updateTestResultDto.vulnerabilityDescription : currentTestResult.vulnerabilityDescription,
        originalSeverity: updateTestResultDto.originalSeverity !== undefined ? updateTestResultDto.originalSeverity : currentTestResult.originalSeverity,
        adjustedSeverity: updateTestResultDto.adjustedSeverity !== undefined ? updateTestResultDto.adjustedSeverity : currentTestResult.adjustedSeverity,
        affectedUrls: updateTestResultDto.affectedUrls !== undefined ? updateTestResultDto.affectedUrls : currentTestResult.affectedUrls,
        requestResponse: updateTestResultDto.requestResponse !== undefined ? updateTestResultDto.requestResponse : currentTestResult.requestResponse,
        remediationGuidance: updateTestResultDto.remediationGuidance !== undefined ? updateTestResultDto.remediationGuidance : currentTestResult.remediationGuidance,
        // Only copy artifact URLs if they are explicitly provided in the update
        // For manual updates, these will be undefined and should remain null
        screenshotUrl: updateTestResultDto.screenshotUrl !== undefined ? updateTestResultDto.screenshotUrl : null,
        videoUrl: updateTestResultDto.videoUrl !== undefined ? updateTestResultDto.videoUrl : null,
        sequence: nextSequence,
        previousResultId: currentTestResult.id,
        isLatest: true,
        createdBy: createdBy
      });

      // 8. Save the new test result
      const savedTestResult = await manager.save(TestResult, newTestResult);

      // 9. Handle logs storage if logs are provided (same logic as createTestResult)
      if (updateTestResultDto.logs && updateTestResultDto.logs.length > 0) {
        try {
          const logsUrl = await this.storageService.uploadLogs(
            {
              projectId: projectId,
              testCaseId: currentTestResult.testCaseId,
              testRunId: currentTestResult.testRunId,
              testResultId: savedTestResult.id
            },
            updateTestResultDto.logs
          );
          savedTestResult.logsUrl = logsUrl;
          await manager.save(TestResult, savedTestResult);
        } catch (error) {
          console.error(`Failed to upload logs for test result ${savedTestResult.id}:`, error);
          // Continue without failing the entire operation
        }
      }

      return savedTestResult;
    });
  }

  async bulkUpdateTestResults(
    testRunId: string,
    projectId: string,
    userId: string,
    testResultIds: string[],
    updateTestResultDto: Partial<CreateTestResultDto>
  ): Promise<{ updated: number, message: string }> {
    // Verify access to the test run
    await this.findOne(testRunId, projectId, userId);

    // Get user information for createdBy field
    let createdBy = 'unknown';

    // First, try to use createdBy from DTO if provided (from websocket)
    if (updateTestResultDto.createdBy) {
      createdBy = updateTestResultDto.createdBy;
      console.log('Using createdBy from DTO:', createdBy);
    } else {
      // Fallback to user lookup from JWT token
      try {
        const user = await this.usersService.findById(userId);
        if (user) {
          createdBy = user.name || user.email || 'unknown';
          console.log('Using createdBy from user lookup:', createdBy);
        }
      } catch (error) {
        console.warn('Failed to get user information for createdBy field:', error);
      }
    }

    // Use a transaction to ensure all updates are atomic
    return this.dataSource.transaction(async manager => {
      let updatedCount = 0;

      // Process each test result
      for (const id of testResultIds) {
        try {
          // 1. First lock the test result
          await manager.query(
            `SELECT 1 FROM test_results WHERE id = $1 FOR UPDATE`,
            [id]
          );

          // 2. Then load with relations
          const currentTestResult = await manager.findOne(TestResult, {
            where: { id, isLatest: true },
            relations: ['testRun', 'testCase']
          });

          // Skip if not found or not in the correct project
          if (!currentTestResult || currentTestResult.testRun.projectId !== projectId) {
            continue;
          }

          // 3. Find the highest sequence number for this test case in this test run
          const maxSequenceResult = await manager.createQueryBuilder()
            .select('MAX(sequence)', 'maxSequence')
            .from(TestResult, 'tr')
            .where('tr.testCaseId = :testCaseId', { testCaseId: currentTestResult.testCaseId })
            .andWhere('tr.testRunId = :testRunId', { testRunId: currentTestResult.testRunId })
            .getRawOne();

          const nextSequence = maxSequenceResult.maxSequence + 1;

          // 4. Mark the current result as not latest
          await manager.update(TestResult, { id }, { isLatest: false });

          // 5. Extract duration from logs if available (same logic as createTestResult)
          let duration: number | null = updateTestResultDto.duration || currentTestResult.duration;
          if (updateTestResultDto.logs && updateTestResultDto.logs.length > 0) {
            const executionTimeLog = updateTestResultDto.logs.find(log =>
              log.includes('Execution time:') || log.includes('⏱️ Execution time:')
            );
            if (executionTimeLog) {
              const match = executionTimeLog.match(/(\d+)ms/);
              if (match) {
                duration = parseInt(match[1]);
              }
            }
          }

          // 6. Create a new test result record
          const newTestResult = manager.create(TestResult, {
            testRunId: currentTestResult.testRunId,
            testCaseId: currentTestResult.testCaseId,
            status: updateTestResultDto.status || currentTestResult.status,
            actualResult: updateTestResultDto.actualResult !== undefined ? updateTestResultDto.actualResult : currentTestResult.actualResult,
            notes: updateTestResultDto.notes !== undefined ? updateTestResultDto.notes : currentTestResult.notes,
            duration: duration, // Use extracted duration, not executionTime
            // DAST-specific fields
            vulnerabilityDescription: updateTestResultDto.vulnerabilityDescription !== undefined ? updateTestResultDto.vulnerabilityDescription : currentTestResult.vulnerabilityDescription,
            originalSeverity: updateTestResultDto.originalSeverity !== undefined ? updateTestResultDto.originalSeverity : currentTestResult.originalSeverity,
            adjustedSeverity: updateTestResultDto.adjustedSeverity !== undefined ? updateTestResultDto.adjustedSeverity : currentTestResult.adjustedSeverity,
            affectedUrls: updateTestResultDto.affectedUrls !== undefined ? updateTestResultDto.affectedUrls : currentTestResult.affectedUrls,
            requestResponse: updateTestResultDto.requestResponse !== undefined ? updateTestResultDto.requestResponse : currentTestResult.requestResponse,
            remediationGuidance: updateTestResultDto.remediationGuidance !== undefined ? updateTestResultDto.remediationGuidance : currentTestResult.remediationGuidance,
            // Only copy artifact URLs if they are explicitly provided in the update
            // For manual updates, these will be undefined and should remain null
            screenshotUrl: updateTestResultDto.screenshotUrl !== undefined ? updateTestResultDto.screenshotUrl : null,
            videoUrl: updateTestResultDto.videoUrl !== undefined ? updateTestResultDto.videoUrl : null,
            sequence: nextSequence,
            previousResultId: currentTestResult.id,
            isLatest: true,
            createdBy: createdBy
          });

          // 7. Save the new test result
          const savedTestResult = await manager.save(TestResult, newTestResult);

          // 8. Handle logs storage if logs are provided (same logic as createTestResult)
          if (updateTestResultDto.logs && updateTestResultDto.logs.length > 0) {
            try {
              const logsUrl = await this.storageService.uploadLogs(
                {
                  projectId: projectId,
                  testCaseId: currentTestResult.testCaseId,
                  testRunId: currentTestResult.testRunId,
                  testResultId: savedTestResult.id
                },
                updateTestResultDto.logs
              );
              savedTestResult.logsUrl = logsUrl;
              await manager.save(TestResult, savedTestResult);
            } catch (error) {
              console.error(`Failed to upload logs for test result ${savedTestResult.id}:`, error);
              // Continue without failing the entire operation
            }
          }
          updatedCount++;
        } catch (error) {
          console.error(`Error updating test result ${id}:`, error);
          // Continue with other test results even if one fails
        }
      }

      return {
        updated: updatedCount,
        message: `Successfully updated ${updatedCount} test results`
      };
    });
  }

  async updateTestResultByTestCaseId(
    testRunId: string,
    tcId: string,
    projectId: string,
    userId: string,
    updateTestResultDto: Partial<CreateTestResultDto>,
  ): Promise<TestResult> {
    return this.testResultsRepository.manager.transaction(async (manager) => {
      // find testResult by testCase Id, project Id and testRunId
      const currentTestResult = await manager.findOne(TestResult, {
        where: {
          testCase: { tcId: parseInt(tcId, 10) },
          testRun: { projectId: projectId, id: testRunId },
          isLatest: true
        },
        relations: ['testRun', 'testCase'],
      });

      if (!currentTestResult) {
        throw new NotFoundException('Test result not found for the given test case ID, project ID and test run ID');
      }

      // 1. First lock the test result
      await manager.query(
        `SELECT 1 FROM test_results WHERE id = $1 FOR UPDATE`,
        [currentTestResult.id]
      );

      // 2. Find the highest sequence number for this test case in this test run
      const maxSequenceResult = await manager.createQueryBuilder()
        .select('MAX(sequence)', 'maxSequence')
        .from(TestResult, 'tr')
        .where('tr.testCaseId = :testCaseId', { testCaseId: currentTestResult.testCaseId })
        .andWhere('tr.testRunId = :testRunId', { testRunId: currentTestResult.testRunId })
        .getRawOne();

      const nextSequence = maxSequenceResult.maxSequence + 1;

      // 3. Mark the current result as not latest
      await manager.update(TestResult, { id: currentTestResult.id }, { isLatest: false });

      // 4. Extract duration from logs if available (same logic as createTestResult)
      let duration: number | null = updateTestResultDto.duration || currentTestResult.duration;
      if (updateTestResultDto.logs && updateTestResultDto.logs.length > 0) {
        const executionTimeLog = updateTestResultDto.logs.find(log =>
          log.includes('Execution time:') || log.includes('⏱️ Execution time:')
        );
        if (executionTimeLog) {
          const match = executionTimeLog.match(/(\d+)ms/);
          if (match) {
            duration = parseInt(match[1]);
          }
        }
      }

      // 5. Get user information for createdBy field
      let createdBy = 'unknown';

      // First, try to use createdBy from DTO if provided (from websocket)
      if (updateTestResultDto.createdBy) {
        createdBy = updateTestResultDto.createdBy;
        console.log('Using createdBy from DTO:', createdBy);
      } else {
        // Fallback to user lookup from JWT token
        try {
          const user = await this.usersService.findById(userId);
          if (user) {
            createdBy = user.name || user.email || 'unknown';
            console.log('Using createdBy from user lookup:', createdBy);
          }
        } catch (error) {
          console.warn('Failed to get user information for createdBy field:', error);
        }
      }

      // 6. Determine the appropriate status for DAST tests
      let finalStatus = updateTestResultDto.status || currentTestResult.status;

      // Check if this is a DAST test and if it has security data
      const isDastTest = currentTestResult.logsSecurityUrl ||
                        currentTestResult.vulnerabilityDescription ||
                        updateTestResultDto.logsSecurityUrl ||
                        updateTestResultDto.vulnerabilityDescription;

      // For DAST tests, if the current status is "New" (set by security report processing),
      // preserve it instead of overwriting with "passed"/"failed"
      if (isDastTest && currentTestResult.status === TestResultStatus.NEW) {
        console.log(`🔒 DAST test detected: Preserving "New" status for test result ${currentTestResult.id}`);
        finalStatus = TestResultStatus.NEW;
      }

      // 7. Create a new test result record
      const newTestResult = manager.create(TestResult, {
        testRunId: currentTestResult.testRunId,
        testCaseId: currentTestResult.testCaseId,
        status: finalStatus,
        actualResult: updateTestResultDto.actualResult !== undefined ? updateTestResultDto.actualResult : currentTestResult.actualResult,
        notes: updateTestResultDto.notes !== undefined ? updateTestResultDto.notes : currentTestResult.notes,
        duration: duration, // Use extracted duration, not executionTime
        // DAST-specific fields
        vulnerabilityDescription: updateTestResultDto.vulnerabilityDescription !== undefined ? updateTestResultDto.vulnerabilityDescription : currentTestResult.vulnerabilityDescription,
        originalSeverity: updateTestResultDto.originalSeverity !== undefined ? updateTestResultDto.originalSeverity : currentTestResult.originalSeverity,
        adjustedSeverity: updateTestResultDto.adjustedSeverity !== undefined ? updateTestResultDto.adjustedSeverity : currentTestResult.adjustedSeverity,
        affectedUrls: updateTestResultDto.affectedUrls !== undefined ? updateTestResultDto.affectedUrls : currentTestResult.affectedUrls,
        requestResponse: updateTestResultDto.requestResponse !== undefined ? updateTestResultDto.requestResponse : currentTestResult.requestResponse,
        remediationGuidance: updateTestResultDto.remediationGuidance !== undefined ? updateTestResultDto.remediationGuidance : currentTestResult.remediationGuidance,
        // Only copy artifact URLs if they are explicitly provided in the update
        // For manual updates, these will be undefined and should remain null
        screenshotUrl: updateTestResultDto.screenshotUrl !== undefined ? updateTestResultDto.screenshotUrl : null,
        videoUrl: updateTestResultDto.videoUrl !== undefined ? updateTestResultDto.videoUrl : null,
        sequence: nextSequence,
        previousResultId: currentTestResult.id,
        isLatest: true,
        createdBy: createdBy
      });

      // 8. Save the new test result
      const savedTestResult = await manager.save(TestResult, newTestResult);

      // 9. Handle logs storage if logs are provided (same logic as createTestResult)
      if (updateTestResultDto.logs && updateTestResultDto.logs.length > 0) {
        try {
          const logsUrl = await this.storageService.uploadLogs(
            {
              projectId: projectId,
              testCaseId: currentTestResult.testCaseId,
              testRunId: testRunId,
              testResultId: savedTestResult.id
            },
            updateTestResultDto.logs
          );
          savedTestResult.logsUrl = logsUrl;
          await manager.save(TestResult, savedTestResult);
        } catch (error) {
          console.error(`Failed to upload logs for test result ${savedTestResult.id}:`, error);
          // Continue without failing the entire operation
        }
      }

      return savedTestResult;
    });
  }

  async removeTestResult(id: string, projectId: string, userId: string): Promise<void> {
    const testResult = await this.findOneTestResult(id, projectId, userId);
    await this.testResultsRepository.remove(testResult);
  }

  async getTestResultHistory(testResultId: string, _testRunId: string, projectId: string, userId: string): Promise<TestResult[]> {
    // Verify access
    const currentTestResult = await this.findOneTestResult(testResultId, projectId, userId);

    // Get the test case ID and test run ID from the current test result
    const testCaseId = currentTestResult.testCaseId;
    const testRunIdToUse = currentTestResult.testRunId;

    // Find all test results for this test case in this test run
    const testResults = await this.testResultsRepository.find({
      where: {
        testCaseId,
        testRunId: testRunIdToUse
      },
      order: { sequence: 'DESC' }
    });

    return testResults;
  }

  async createJiraIssue(
    testResultId: string,
    testRunId: string,
    projectId: string,
    userId: string,
    createIssueDto: CreateIssueDto
  ) {
    // First verify access to test result
    const testResult = await this.findOneTestResult(testResultId, projectId, userId);
    if (!testResult) {
      throw new NotFoundException('Test result not found');
    }

    // Start a transaction
    return this.dataSource.transaction(async manager => {
      // Check if issue already exists
      const existingIssue = await manager.getRepository(Issue).findOne({
        where: { testResultId }
      });

      if (existingIssue) {
        throw new Error('Issue already exists for this test result');
      }

      // Get the test run to find its project
      const testRun = await manager.getRepository(TestRun).findOne({
        where: { id: testRunId }
      });

      if (!testRun) {
        throw new NotFoundException(`Test run with ID ${testRunId} not found`);
      }

      // Get the project ID from the test run
      const projectIdFromTestRun = testRun.projectId;

      // Find all test runs for this project
      const testRunsInProject = await manager.getRepository(TestRun)
        .find({
          where: { projectId: projectIdFromTestRun }
        });

      // Get all test run IDs for this project
      const testRunIds = testRunsInProject.map((tr: TestRun) => tr.id);

      // Find the highest defectId across all test runs in this project
      const maxDefectIdResult = await manager.getRepository(Issue)
        .createQueryBuilder('issue')
        .select('MAX(issue.defectId)', 'maxDefectId')
        .where('issue.testRunId IN (:...testRunIds)', { testRunIds })
        .getRawOne();

      // Calculate the next defectId at the project level
      const nextDefectId = maxDefectIdResult && maxDefectIdResult.maxDefectId ? maxDefectIdResult.maxDefectId + 1 : 1;

      // Get the test result to ensure we have the testRunId
      const testResult = await manager.getRepository(TestResult).findOne({
        where: { id: testResultId }
      });

      // Use the testRunId from the test result if available, otherwise use the one passed in
      const finalTestRunId = testResult && testResult.testRunId ? testResult.testRunId : testRunId;

      // Create the issue record
      const issue = await manager.getRepository(Issue).save({
        testResultId,
        testRunId: finalTestRunId,
        defectId: nextDefectId,
        jiraIssueId: createIssueDto.jiraIssueId,
        jiraIssueKey: createIssueDto.jiraIssueKey,
        jiraIssueUrl: createIssueDto.jiraIssueUrl,
        summary: createIssueDto.summary,
        description: createIssueDto.description,
        status: createIssueDto.status || 'To Do'
      });

      return issue;
    });
  }

  async getJiraIssue(
    testResultId: string,
    testRunId: string,
    projectId: string,
    userId: string
  ) {
    // First verify access to test result
    const testResult = await this.findOneTestResult(testResultId, projectId, userId);
    if (!testResult) {
      throw new NotFoundException('Test result not found');
    }

    // Get the issue
    const issue = await this.dataSource.getRepository(Issue).findOne({
      where: { testResultId }
    });

    if (!issue) {
      throw new NotFoundException('No JIRA issue found for this test result');
    }

    // Handle existing records that don't have testRunId or defectId
    let needsUpdate = false;

    // Make sure testRunId is included in the response
    if (!issue.testRunId) {
      // If the issue was created before this field was added, update it
      issue.testRunId = testRunId;
      needsUpdate = true;
    }

    // Make sure defectId is set
    if (!issue.defectId) {
      // Get the test run to find its project
      const testRun = await this.dataSource.getRepository(TestRun).findOne({
        where: { id: testRunId }
      });

      if (!testRun) {
        throw new NotFoundException(`Test run with ID ${testRunId} not found`);
      }

      // Get the project ID from the test run
      const projectIdFromTestRun = testRun.projectId;

      // Find all test runs for this project
      const testRunsInProject = await this.dataSource.getRepository(TestRun)
        .find({
          where: { projectId: projectIdFromTestRun }
        });

      // Get all test run IDs for this project
      const testRunIds = testRunsInProject.map((tr: TestRun) => tr.id);

      // Find the highest defectId across all test runs in this project
      const maxDefectIdResult = await this.dataSource.getRepository(Issue)
        .createQueryBuilder('issue')
        .select('MAX(issue.defectId)', 'maxDefectId')
        .where('issue.testRunId IN (:...testRunIds)', { testRunIds })
        .getRawOne();

      // Calculate the next defectId at the project level
      const nextDefectId = maxDefectIdResult && maxDefectIdResult.maxDefectId ? maxDefectIdResult.maxDefectId + 1 : 1;

      issue.defectId = nextDefectId;
      needsUpdate = true;
    }

    // Save the updated issue if needed
    if (needsUpdate) {
      await this.dataSource.getRepository(Issue).save(issue);
    }

    return issue;
  }

  async syncIssuesWithJira(
    projectId: string,
    userId: string
  ) {
    // First get the user to find their company ID
    const user = await this.dataSource.getRepository(User).findOne({
      where: { id: userId }
    });

    if (!user || !user.companyId) {
      throw new NotFoundException('User not found or has no company');
    }

    // Verify access to project
    const project = await this.projectsService.findOne(projectId, user.companyId);
    if (!project) {
      throw new NotFoundException(`Project with ID ${projectId} not found`);
    }

    // Get all issues for this project that have a JIRA issue key
    const issues = await this.dataSource.getRepository(Issue)
      .createQueryBuilder('issue')
      .leftJoin('issue.testRun', 'testRun')
      .where('testRun.projectId = :projectId', { projectId })
      .andWhere('issue.jiraIssueKey IS NOT NULL')
      .getMany();

    if (issues.length === 0) {
      return {
        message: 'No JIRA issues found to sync',
        updated: 0,
        deleted: 0
      };
    }

    let updatedCount = 0;
    let deletedCount = 0;

    // Process each issue
    for (const issue of issues) {
      try {
        // Check if the issue still exists in JIRA
        const jiraIssue = await this.jiraService.getIssue(issue.jiraIssueKey);

        if (!jiraIssue) {
          // Issue was deleted in JIRA
          issue.status = 'Deleted';
          await this.dataSource.getRepository(Issue).save(issue);
          deletedCount++;
          continue;
        }

        // Update the status if it changed
        if (jiraIssue.fields.status.name !== issue.status) {
          issue.status = jiraIssue.fields.status.name;
          await this.dataSource.getRepository(Issue).save(issue);
          updatedCount++;
        }
      } catch (error) {
        console.error(`Error syncing issue ${issue.jiraIssueKey}:`, error);
      }
    }

    return {
      message: `Successfully synced ${issues.length} issues with JIRA`,
      updated: updatedCount,
      deleted: deletedCount
    };
  }

  async getProjectIssues(
    projectId: string,
    userId: string,
    options: {
      page: number;
      limit: number;
      search?: string;
      sortField?: string;
      sortDirection?: 'ASC' | 'DESC';
      filters?: {
        status?: string[];
        testRunId?: string[];
        createdAfter?: string;
        createdBefore?: string;
      }
    }
  ) {
    // First get the user to find their company ID
    const user = await this.dataSource.getRepository(User).findOne({
      where: { id: userId }
    });

    if (!user || !user.companyId) {
      throw new NotFoundException('User not found or has no company');
    }

    // Verify access to project
    const project = await this.projectsService.findOne(projectId, user.companyId);
    if (!project) {
      throw new NotFoundException(`Project with ID ${projectId} not found`);
    }

    // Build the query
    const queryBuilder = this.dataSource.getRepository(Issue)
      .createQueryBuilder('issue')
      .leftJoin('issue.testRun', 'testRun')
      .where('testRun.projectId = :projectId', { projectId });

    // Add search condition if provided
    if (options.search) {
      queryBuilder.andWhere(
        '(issue.summary ILIKE :search OR issue.description ILIKE :search OR issue.jiraIssueKey ILIKE :search)',
        { search: `%${options.search}%` }
      );
    }

    // Add filters if provided
    if (options.filters) {
      // Filter by status
      if (options.filters.status && options.filters.status.length > 0) {
        queryBuilder.andWhere('issue.status IN (:...statuses)', {
          statuses: options.filters.status
        });
      }

      // Filter by test run
      if (options.filters.testRunId && options.filters.testRunId.length > 0) {
        queryBuilder.andWhere('issue.testRunId IN (:...testRunIds)', {
          testRunIds: options.filters.testRunId
        });
      }

      // Filter by created date range
      if (options.filters.createdAfter) {
        queryBuilder.andWhere('issue.createdAt >= :createdAfter', {
          createdAfter: new Date(options.filters.createdAfter)
        });
      }

      if (options.filters.createdBefore) {
        // Add one day to include the end date
        const endDate = new Date(options.filters.createdBefore);
        endDate.setDate(endDate.getDate() + 1);

        queryBuilder.andWhere('issue.createdAt < :createdBefore', {
          createdBefore: endDate
        });
      }
    }

    // Count total items
    const total = await queryBuilder.getCount();

    // Add sorting
    const sortField = options.sortField || 'createdAt';
    const sortDirection = options.sortDirection || 'DESC';

    // Handle special case for testRunName which is not a direct field
    if (sortField === 'testRunName') {
      queryBuilder.leftJoinAndSelect('issue.testRun', 'sortTestRun')
        .orderBy('sortTestRun.name', sortDirection);
    } else {
      queryBuilder.orderBy(`issue.${sortField}`, sortDirection);
    }

    // Add pagination
    const skip = (options.page - 1) * options.limit;
    queryBuilder
      .skip(skip)
      .take(options.limit);

    // Execute query
    const issues = await queryBuilder.getMany();

    // Fetch test run names for each issue
    const issuesWithTestRunNames = await Promise.all(
      issues.map(async (issue) => {
        if (issue.testRunId) {
          const testRun = await this.dataSource.getRepository(TestRun).findOne({
            where: { id: issue.testRunId },
            select: ['id', 'name']
          });

          return {
            ...issue,
            testRunName: testRun?.name || 'Unknown'
          };
        }
        return {
          ...issue,
          testRunName: 'Unknown'
        };
      })
    );

    // Calculate total pages
    const totalPages = Math.ceil(total / options.limit);

    return {
      issues: issuesWithTestRunNames,
      total,
      page: options.page,
      limit: options.limit,
      totalPages
    };
  }

  // Helper method to get user with companyId
  private async getUserWithCompanyId(userId: string): Promise<{ id: string; companyId: string }> {
    const user = await this.usersService.findById(userId);

    if (!user || !user.companyId) {
      throw new NotFoundException(`User with ID ${userId} not found or has no company`);
    }

    return { id: user.id, companyId: user.companyId };
  }

  async uploadTestResultVideo(
    testResultId: string,
    testRunId: string,
    projectId: string,
    userId: string,
    file: any
  ): Promise<{ message: string; videoUrl?: string }> {
    // Get user's companyId
    const user = await this.getUserWithCompanyId(userId);

    // Verify project exists and belongs to user's company
    await this.projectsService.findOne(projectId, user.companyId);

    // Verify test result exists and belongs to the test run, and get testCase info
    const testResult = await this.testResultsRepository.findOne({
      where: {
        id: testResultId,
        testRunId: testRunId
      },
      relations: ['testCase']
    });

    if (!testResult) {
      throw new NotFoundException(`Test result with ID ${testResultId} not found in test run ${testRunId}`);
    }

    // Validate file
    if (!file) {
      throw new Error('No video file provided');
    }

    // Validate file type
    if (!file.mimetype.startsWith('video/')) {
      throw new Error('File must be a video');
    }

    try {
      // Upload video to Google Cloud Storage with testCaseId
      const videoUrl = await this.storageService.uploadVideo(
        projectId,
        testRunId,
        testResult.testCaseId,
        testResultId,
        file.buffer,
        file.mimetype
      );

      // Update test result with video URL
      console.log(`Updating test result ${testResultId} with video URL: ${videoUrl}`);
      const updateResult = await this.testResultsRepository.update(testResultId, {
        videoUrl: videoUrl
      });
      console.log(`Database update result:`, updateResult);

      // Verify the update worked
      const updatedTestResult = await this.testResultsRepository.findOne({
        where: { id: testResultId }
      });
      console.log(`Verified test result after update:`, {
        id: updatedTestResult?.id,
        videoUrl: updatedTestResult?.videoUrl,
        status: updatedTestResult?.status
      });

      console.log(`Video uploaded successfully for test result ${testResultId}: ${videoUrl}`);

      return {
        message: 'Video uploaded successfully',
        videoUrl: videoUrl
      };
    } catch (error) {
      console.error('Failed to upload video:', error);
      throw new Error(`Failed to upload video: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async uploadTestResultScreenshot(
    testResultId: string,
    testRunId: string,
    projectId: string,
    userId: string,
    file: any
  ): Promise<{ message: string; screenshotUrl: string }> {
    try {
      // Verify access to the test result and get testCase info
      const testResult = await this.testResultsRepository.findOne({
        where: { id: testResultId },
        relations: ['testRun', 'testCase']
      });

      if (!testResult || testResult.testRun.projectId !== projectId) {
        throw new NotFoundException('Test result not found');
      }

      // Verify access
      await this.findOne(testResult.testRunId, projectId, userId);

      if (!file || !file.buffer) {
        throw new Error('No screenshot file provided');
      }

      // Upload screenshot to Google Cloud Storage with testCaseId
      const screenshotUrl = await this.storageService.uploadScreenshot(
        projectId,
        testRunId,
        testResult.testCaseId,
        testResultId,
        file.buffer,
        file.mimetype
      );

      // Update test result with screenshot URL
      console.log(`Updating test result ${testResultId} with screenshot URL: ${screenshotUrl}`);
      const updateResult = await this.testResultsRepository.update(testResultId, {
        screenshotUrl: screenshotUrl
      });
      console.log(`Database update result:`, updateResult);

      // Verify the update worked
      const updatedTestResult = await this.testResultsRepository.findOne({
        where: { id: testResultId }
      });
      console.log(`Verified test result after update:`, {
        id: updatedTestResult?.id,
        screenshotUrl: updatedTestResult?.screenshotUrl,
        status: updatedTestResult?.status
      });

      console.log(`Screenshot uploaded successfully for test result ${testResultId}: ${screenshotUrl}`);

      return {
        message: 'Screenshot uploaded successfully',
        screenshotUrl: screenshotUrl
      };
    } catch (error) {
      console.error('Failed to upload screenshot:', error);
      throw new Error(`Failed to upload screenshot: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async getSignedUrl(gcsUrl: string): Promise<string> {
    try {
      return await this.storageService.getSignedUrl(gcsUrl);
    } catch (error) {
      console.error('Failed to get signed URL:', error);
      throw new Error(`Failed to get signed URL: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}
